{"name": "Collect", "preName": null, "comment": "收藏：", "templateGroupName": null, "fullColumn": [{"name": "collectId", "comment": null, "type": null, "shortType": null, "custom": false, "ext": {}}, {"name": "userId", "comment": null, "type": null, "shortType": null, "custom": false, "ext": {}}, {"name": "sourceTable", "comment": null, "type": null, "shortType": null, "custom": false, "ext": {}}, {"name": "sourceField", "comment": null, "type": null, "shortType": null, "custom": false, "ext": {}}, {"name": "sourceId", "comment": null, "type": null, "shortType": null, "custom": false, "ext": {}}, {"name": "title", "comment": null, "type": null, "shortType": null, "custom": false, "ext": {}}, {"name": "img", "comment": null, "type": null, "shortType": null, "custom": false, "ext": {}}, {"name": "createTime", "comment": null, "type": null, "shortType": null, "custom": false, "ext": {}}, {"name": "updateTime", "comment": null, "type": null, "shortType": null, "custom": false, "ext": {}}], "pkColumn": null, "otherColumn": null, "savePackageName": "com.linglouyi.changsheng", "savePath": "./src/main/java/com/linglouyi/changsheng", "saveModelName": "changsheng-spring boot"}
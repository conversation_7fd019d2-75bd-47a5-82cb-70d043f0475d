{"name": "Ad", "preName": "", "comment": "广告信息：", "templateGroupName": "jpa", "fullColumn": [{"name": "adId", "comment": null, "type": null, "shortType": null, "custom": false, "ext": {}}, {"name": "display", "comment": null, "type": null, "shortType": null, "custom": false, "ext": {}}, {"name": "hits", "comment": null, "type": null, "shortType": null, "custom": false, "ext": {}}, {"name": "location", "comment": null, "type": null, "shortType": null, "custom": false, "ext": {}}, {"name": "title", "comment": null, "type": null, "shortType": null, "custom": false, "ext": {}}, {"name": "content", "comment": null, "type": null, "shortType": null, "custom": false, "ext": {}}, {"name": "img", "comment": null, "type": null, "shortType": null, "custom": false, "ext": {}}, {"name": "url", "comment": null, "type": null, "shortType": null, "custom": false, "ext": {}}, {"name": "createTime", "comment": null, "type": null, "shortType": null, "custom": false, "ext": {}}, {"name": "updateTime", "comment": null, "type": null, "shortType": null, "custom": false, "ext": {}}], "pkColumn": null, "otherColumn": null, "savePackageName": "com.linglouyi.changsheng", "savePath": "./src/main/java/com/linglouyi/changsheng", "saveModelName": "changsheng-spring boot"}
{"name": "Comment", "preName": null, "comment": "评论：", "templateGroupName": null, "fullColumn": [{"name": "commentId", "comment": null, "type": null, "shortType": null, "custom": false, "ext": {}}, {"name": "userId", "comment": null, "type": null, "shortType": null, "custom": false, "ext": {}}, {"name": "replyToId", "comment": null, "type": null, "shortType": null, "custom": false, "ext": {}}, {"name": "content", "comment": null, "type": null, "shortType": null, "custom": false, "ext": {}}, {"name": "nickname", "comment": null, "type": null, "shortType": null, "custom": false, "ext": {}}, {"name": "avatar", "comment": null, "type": null, "shortType": null, "custom": false, "ext": {}}, {"name": "createTime", "comment": null, "type": null, "shortType": null, "custom": false, "ext": {}}, {"name": "updateTime", "comment": null, "type": null, "shortType": null, "custom": false, "ext": {}}, {"name": "sourceTable", "comment": null, "type": null, "shortType": null, "custom": false, "ext": {}}, {"name": "sourceField", "comment": null, "type": null, "shortType": null, "custom": false, "ext": {}}, {"name": "sourceId", "comment": null, "type": null, "shortType": null, "custom": false, "ext": {}}], "pkColumn": null, "otherColumn": null, "savePackageName": "com.linglouyi.changsheng", "savePath": "./src/main/java/com/linglouyi/changsheng", "saveModelName": "changsheng-spring boot"}
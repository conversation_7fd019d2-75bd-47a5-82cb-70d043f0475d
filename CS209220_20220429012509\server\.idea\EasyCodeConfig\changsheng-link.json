{"name": "Link", "preName": "", "comment": "友情链接：", "templateGroupName": "jpa", "fullColumn": [{"name": "linkId", "comment": null, "type": null, "shortType": null, "custom": false, "ext": {}}, {"name": "display", "comment": null, "type": null, "shortType": null, "custom": false, "ext": {}}, {"name": "name", "comment": null, "type": null, "shortType": null, "custom": false, "ext": {}}, {"name": "img", "comment": null, "type": null, "shortType": null, "custom": false, "ext": {}}, {"name": "url", "comment": null, "type": null, "shortType": null, "custom": false, "ext": {}}, {"name": "createTime", "comment": null, "type": null, "shortType": null, "custom": false, "ext": {}}, {"name": "updateTime", "comment": null, "type": null, "shortType": null, "custom": false, "ext": {}}], "pkColumn": null, "otherColumn": null, "savePackageName": "com.linglouyi.changsheng", "savePath": "./src/main/java/com/linglouyi/changsheng", "saveModelName": "changsheng-spring boot"}
{"name": "ArticleType", "preName": "", "comment": "文章频道：用于汇总浏览文章，在不同频道下展示不同文章。", "templateGroupName": "jpa", "fullColumn": [{"name": "typeId", "comment": null, "type": null, "shortType": null, "custom": false, "ext": {}}, {"name": "display", "comment": null, "type": null, "shortType": null, "custom": false, "ext": {}}, {"name": "name", "comment": null, "type": null, "shortType": null, "custom": false, "ext": {}}, {"name": "fatherId", "comment": null, "type": null, "shortType": null, "custom": false, "ext": {}}, {"name": "description", "comment": null, "type": null, "shortType": null, "custom": false, "ext": {}}, {"name": "icon", "comment": null, "type": null, "shortType": null, "custom": false, "ext": {}}, {"name": "url", "comment": null, "type": null, "shortType": null, "custom": false, "ext": {}}, {"name": "createTime", "comment": null, "type": null, "shortType": null, "custom": false, "ext": {}}, {"name": "updateTime", "comment": null, "type": null, "shortType": null, "custom": false, "ext": {}}], "pkColumn": null, "otherColumn": null, "savePackageName": "com.linglouyi.changsheng", "savePath": "./src/main/java/com/linglouyi/changsheng", "saveModelName": "changsheng-spring boot"}
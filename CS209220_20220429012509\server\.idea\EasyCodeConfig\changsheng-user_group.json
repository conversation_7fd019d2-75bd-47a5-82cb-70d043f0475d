{"name": "UserGroup", "preName": null, "comment": "用户组：用于用户前端身份和鉴权", "templateGroupName": null, "fullColumn": [{"name": "groupId", "comment": null, "type": null, "shortType": null, "custom": false, "ext": {}}, {"name": "display", "comment": null, "type": null, "shortType": null, "custom": false, "ext": {}}, {"name": "name", "comment": null, "type": null, "shortType": null, "custom": false, "ext": {}}, {"name": "description", "comment": null, "type": null, "shortType": null, "custom": false, "ext": {}}, {"name": "sourceTable", "comment": null, "type": null, "shortType": null, "custom": false, "ext": {}}, {"name": "sourceField", "comment": null, "type": null, "shortType": null, "custom": false, "ext": {}}, {"name": "sourceId", "comment": null, "type": null, "shortType": null, "custom": false, "ext": {}}, {"name": "register", "comment": null, "type": null, "shortType": null, "custom": false, "ext": {}}], "pkColumn": null, "otherColumn": null, "savePackageName": "com.linglouyi.changsheng", "savePath": "./src/main/java/com/linglouyi/changsheng", "saveModelName": "changsheng-spring boot"}
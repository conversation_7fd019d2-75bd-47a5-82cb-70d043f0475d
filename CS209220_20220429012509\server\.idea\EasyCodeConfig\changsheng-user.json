{"name": "User", "preName": null, "comment": "用户账户：用于保存用户登录信息", "templateGroupName": null, "fullColumn": [{"name": "userId", "comment": null, "type": null, "shortType": null, "custom": false, "ext": {}}, {"name": "state", "comment": null, "type": null, "shortType": null, "custom": false, "ext": {}}, {"name": "userGroup", "comment": null, "type": null, "shortType": null, "custom": false, "ext": {}}, {"name": "loginTime", "comment": null, "type": null, "shortType": null, "custom": false, "ext": {}}, {"name": "phone", "comment": null, "type": null, "shortType": null, "custom": false, "ext": {}}, {"name": "phoneState", "comment": null, "type": null, "shortType": null, "custom": false, "ext": {}}, {"name": "username", "comment": null, "type": null, "shortType": null, "custom": false, "ext": {}}, {"name": "nickname", "comment": null, "type": null, "shortType": null, "custom": false, "ext": {}}, {"name": "password", "comment": null, "type": null, "shortType": null, "custom": false, "ext": {}}, {"name": "email", "comment": null, "type": null, "shortType": null, "custom": false, "ext": {}}, {"name": "emailState", "comment": null, "type": null, "shortType": null, "custom": false, "ext": {}}, {"name": "avatar", "comment": null, "type": null, "shortType": null, "custom": false, "ext": {}}, {"name": "createTime", "comment": null, "type": null, "shortType": null, "custom": false, "ext": {}}], "pkColumn": null, "otherColumn": null, "savePackageName": "com.linglouyi.changsheng", "savePath": "./src/main/java/com/linglouyi/changsheng", "saveModelName": "changsheng-spring boot"}
<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="CompilerConfiguration">
    <annotationProcessing>
      <profile default="true" name="Default" enabled="true" />
      <profile name="Maven default annotation processors profile" enabled="true">
        <sourceOutputDir name="target/generated-sources/annotations" />
        <sourceTestOutputDir name="target/generated-test-sources/test-annotations" />
        <outputRelativeToContentRoot value="true" />
        <module name="changsheng-spring boot" />
        <module name="changsheng-springboot" />
        <module name="project-spring_boot" />
        <module name="server" />
      </profile>
    </annotationProcessing>
  </component>
  <component name="JavacSettings">
    <option name="ADDITIONAL_OPTIONS_OVERRIDE">
      <module name="changsheng-spring boot" options="-parameters" />
      <module name="changsheng-springboot" options="-parameters" />
      <module name="project-spring_boot" options="-parameters" />
      <module name="server" options="-parameters" />
    </option>
  </component>
</project>
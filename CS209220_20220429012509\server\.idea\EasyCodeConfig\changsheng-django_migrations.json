{"name": "DjangoMigrations", "preName": null, "comment": null, "templateGroupName": null, "fullColumn": [{"name": "id", "comment": null, "type": null, "shortType": null, "custom": false, "ext": {}}, {"name": "app", "comment": null, "type": null, "shortType": null, "custom": false, "ext": {}}, {"name": "name", "comment": null, "type": null, "shortType": null, "custom": false, "ext": {}}, {"name": "applied", "comment": null, "type": null, "shortType": null, "custom": false, "ext": {}}], "pkColumn": null, "otherColumn": null, "savePackageName": "com.linglouyi.changsheng", "savePath": "./src/main/java/com/linglouyi/changsheng", "saveModelName": "changsheng-spring boot"}
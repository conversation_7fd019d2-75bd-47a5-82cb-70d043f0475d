{"name": "<PERSON><PERSON>", "preName": null, "comment": "购物车：", "templateGroupName": null, "fullColumn": [{"name": "cartId", "comment": null, "type": null, "shortType": null, "custom": false, "ext": {}}, {"name": "title", "comment": null, "type": null, "shortType": null, "custom": false, "ext": {}}, {"name": "img", "comment": null, "type": null, "shortType": null, "custom": false, "ext": {}}, {"name": "userId", "comment": null, "type": null, "shortType": null, "custom": false, "ext": {}}, {"name": "createTime", "comment": null, "type": null, "shortType": null, "custom": false, "ext": {}}, {"name": "updateTime", "comment": null, "type": null, "shortType": null, "custom": false, "ext": {}}, {"name": "state", "comment": null, "type": null, "shortType": null, "custom": false, "ext": {}}, {"name": "price", "comment": null, "type": null, "shortType": null, "custom": false, "ext": {}}, {"name": "priceAgo", "comment": null, "type": null, "shortType": null, "custom": false, "ext": {}}, {"name": "priceCount", "comment": null, "type": null, "shortType": null, "custom": false, "ext": {}}, {"name": "num", "comment": null, "type": null, "shortType": null, "custom": false, "ext": {}}, {"name": "goodsId", "comment": null, "type": null, "shortType": null, "custom": false, "ext": {}}, {"name": "type", "comment": null, "type": null, "shortType": null, "custom": false, "ext": {}}, {"name": "description", "comment": null, "type": null, "shortType": null, "custom": false, "ext": {}}], "pkColumn": null, "otherColumn": null, "savePackageName": "com.linglouyi.changsheng", "savePath": "./src/main/java/com/linglouyi/changsheng", "saveModelName": "changsheng-spring boot"}
<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.project.demo.dao.UploadMapper">

    <resultMap type="com.project.demo.entity.Upload" id="UploadResult">
        <result property="uploadId"    column="upload_id"    />
        <result property="name"    column="name"    />
        <result property="path"    column="path"    />
        <result property="file"    column="file"    />
        <result property="display"    column="display"    />
        <result property="fatherId"    column="father_id"    />
        <result property="dir"    column="dir"    />
        <result property="type"    column="type"    />
    </resultMap>

    <sql id="selectUploadVo">
        select upload_id, name, path, file, display, father_id, dir, type from upload
    </sql>

    <select id="selectUploadList" parameterType="com.project.demo.entity.Upload" resultMap="UploadResult">
        <include refid="selectUploadVo"/>
        <where>
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="path != null  and path != ''"> and path = #{path}</if>
            <if test="file != null  and file != ''"> and file = #{file}</if>
            <if test="display != null  and display != ''"> and display = #{display}</if>
            <if test="fatherId != null "> and father_id = #{fatherId}</if>
            <if test="dir != null  and dir != ''"> and dir = #{dir}</if>
            <if test="type != null  and type != ''"> and type = #{type}</if>
        </where>
    </select>

    <select id="selectUploadById" parameterType="Long" resultMap="UploadResult">
        <include refid="selectUploadVo"/>
        where upload_id = #{uploadId}
    </select>

    <insert id="insertUpload" parameterType="com.project.demo.entity.Upload" useGeneratedKeys="true" keyProperty="uploadId">
        insert into upload
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="name != null  and name != ''">name,</if>
            <if test="path != null  and path != ''">path,</if>
            <if test="file != null  and file != ''">file,</if>
            <if test="display != null  and display != ''">display,</if>
            <if test="fatherId != null ">father_id,</if>
            <if test="dir != null  and dir != ''">dir,</if>
            <if test="type != null  and type != ''">type,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="name != null  and name != ''">#{name},</if>
            <if test="path != null  and path != ''">#{path},</if>
            <if test="file != null  and file != ''">#{file},</if>
            <if test="display != null  and display != ''">#{display},</if>
            <if test="fatherId != null ">#{fatherId},</if>
            <if test="dir != null  and dir != ''">#{dir},</if>
            <if test="type != null  and type != ''">#{type},</if>
         </trim>
    </insert>

    <update id="updateUpload" parameterType="com.project.demo.entity.Upload">
        update upload
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null  and name != ''">name = #{name},</if>
            <if test="path != null  and path != ''">path = #{path},</if>
            <if test="file != null  and file != ''">file = #{file},</if>
            <if test="display != null  and display != ''">display = #{display},</if>
            <if test="fatherId != null ">father_id = #{fatherId},</if>
            <if test="dir != null  and dir != ''">dir = #{dir},</if>
            <if test="type != null  and type != ''">type = #{type},</if>
        </trim>
        where upload_id = #{uploadId}
    </update>

    <delete id="deleteUploadById" parameterType="Long">
        delete from upload where upload_id = #{uploadId}
    </delete>

    <delete id="deleteUploadByIds" parameterType="String">
        delete from upload where upload_id in
        <foreach item="uploadId" collection="array" open="(" separator="," close=")">
            #{uploadId}
        </foreach>
    </delete>

</mapper>

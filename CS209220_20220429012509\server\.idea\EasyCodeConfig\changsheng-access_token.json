{"name": "AccessToken", "preName": "", "comment": "临时访问牌", "templateGroupName": "jpa", "fullColumn": [{"name": "tokenId", "comment": null, "type": null, "shortType": null, "custom": false, "ext": {}}, {"name": "token", "comment": null, "type": null, "shortType": null, "custom": false, "ext": {}}, {"name": "maxage", "comment": null, "type": null, "shortType": null, "custom": false, "ext": {}}, {"name": "createTime", "comment": null, "type": null, "shortType": null, "custom": false, "ext": {}}, {"name": "updateTime", "comment": null, "type": null, "shortType": null, "custom": false, "ext": {}}, {"name": "info", "comment": null, "type": null, "shortType": null, "custom": false, "ext": {}}], "pkColumn": null, "otherColumn": null, "savePackageName": "com.linglouyi.changsheng", "savePath": "./src/main/java/com/linglouyi/changsheng", "saveModelName": "changsheng-spring boot"}
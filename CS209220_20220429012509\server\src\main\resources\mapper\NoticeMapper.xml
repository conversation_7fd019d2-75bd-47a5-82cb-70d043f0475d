<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.project.demo.dao.NoticeMapper">

    <resultMap type="com.project.demo.entity.Notice" id="NoticeResult">
        <result property="noticeId"    column="notice_id"    />
        <result property="title"    column="title"    />
        <result property="content"    column="content"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectNoticeVo">
        select notice_id, title, content, create_time, update_time from notice
    </sql>

    <select id="selectNoticeList" parameterType="com.project.demo.entity.Notice" resultMap="NoticeResult">
        <include refid="selectNoticeVo"/>
        <where>
            <if test="title != null  and title != ''"> and title = #{title}</if>
            <if test="content != null  and content != ''"> and content = #{content}</if>
        </where>
    </select>

    <select id="selectNoticeById" parameterType="Integer" resultMap="NoticeResult">
        <include refid="selectNoticeVo"/>
        where notice_id = #{noticeId}
    </select>

    <insert id="insertNotice" parameterType="com.project.demo.entity.Notice" useGeneratedKeys="true" keyProperty="noticeId">
        insert into notice
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="title != null  and title != ''">title,</if>
            <if test="content != null  and content != ''">content,</if>
            <if test="createTime != null ">create_time,</if>
            <if test="updateTime != null ">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="title != null  and title != ''">#{title},</if>
            <if test="content != null  and content != ''">#{content},</if>
            <if test="createTime != null ">#{createTime},</if>
            <if test="updateTime != null ">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateNotice" parameterType="com.project.demo.entity.Notice">
        update notice
        <trim prefix="SET" suffixOverrides=",">
            <if test="title != null  and title != ''">title = #{title},</if>
            <if test="content != null  and content != ''">content = #{content},</if>
            <if test="createTime != null ">create_time = #{createTime},</if>
            <if test="updateTime != null ">update_time = #{updateTime},</if>
        </trim>
        where notice_id = #{noticeId}
    </update>

    <delete id="deleteNoticeById" parameterType="Integer">
        delete from notice where notice_id = #{noticeId}
    </delete>

    <delete id="deleteNoticeByIds" parameterType="String">
        delete from notice where notice_id in
        <foreach item="noticeId" collection="array" open="(" separator="," close=")">
            #{noticeId}
        </foreach>
    </delete>

</mapper>
